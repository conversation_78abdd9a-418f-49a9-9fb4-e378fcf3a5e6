# WebWeaver Landing Page

This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app). It features a responsive landing page for a web development service with a contact form that sends email notifications using [Resend](https://resend.com).

## Getting Started

### Prerequisites

1. Create a [Resend](https://resend.com) account
2. Verify your domain in the Resend dashboard
3. Create an API key in the Resend dashboard

### Environment Setup

1. Copy the `.env.local.example` file to `.env.local`:

```bash
cp .env.local.example .env.local
```

2. Update the `.env.local` file with your Resend API key and the email address where you want to receive form submissions:

```
RESEND_API_KEY=your_resend_api_key_here
NOTIFICATION_EMAIL=<EMAIL>
```

### Running the Development Server

Install dependencies:

```bash
npm install
# or
yarn
# or
pnpm install
```

Then, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load Inter and Poppins fonts.

## Contact Form and Email Functionality

The landing page includes a contact form that collects the following information:

- First Name
- Last Name
- Country
- Hometown
- Email Address
- Message

When a user submits the form, the following happens:

1. The form data is validated on the client side
2. The data is sent to the `/api/send` endpoint
3. The server validates the data using Zod
4. Resend sends an email to the address specified in the `NOTIFICATION_EMAIL` environment variable
5. The user receives a success message

### Customizing the Email Template

You can customize the email template by editing the `src/components/email-template.tsx` file. This file uses React components to generate the HTML for the email.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

To learn more about Resend, check out:

- [Resend Documentation](https://resend.com/docs) - learn about Resend features and API.
- [React Email](https://react.email) - build and send emails using React.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
