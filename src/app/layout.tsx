import type { Metadata } from "next";
import { Inter, Poppins } from "next/font/google";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

const poppins = Poppins({
  variable: "--font-poppins",
  weight: ["400", "500", "600", "700"],
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "WebWeaver | Expert Web Development Solutions",
  description: "Bespoke, agentic web solutions for innovative clients. We create high-quality websites, interactive web applications, and conversion-focused landing pages.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${inter.variable} ${poppins.variable} antialiased bg-gradient-to-b from-background to-background-secondary text-foreground min-h-screen`}
      >
        {children}
      </body>
    </html>
  );
}
