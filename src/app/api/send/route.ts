import { NextRequest, NextResponse } from 'next/server';
import { Resend } from 'resend';
import { z } from 'zod';

// Initialize Resend with API key
const resend = new Resend(process.env.RESEND_API_KEY);

// Define validation schema for form data
const formSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  country: z.string().min(1, 'Country is required'),
  hometown: z.string().min(1, 'Hometown is required'),
  email: z.string().email('Invalid email address'),
  message: z.string().min(1, 'Message is required'),
});

export async function POST(req: NextRequest) {
  try {
    // Parse the request body
    const body = await req.json();
    
    // Validate form data
    const result = formSchema.safeParse(body);
    
    if (!result.success) {
      // Return validation errors
      return NextResponse.json(
        { error: 'Validation failed', details: result.error.format() },
        { status: 400 }
      );
    }
    
    const { firstName, lastName, country, hometown, email, message } = result.data;
    
    // Create email content as HTML string instead of React component to avoid type issues
    const emailHtml = `
      <div>
        <h1>New Contact Form Submission</h1>
        <p>You have received a new message from your landing page contact form.</p>
        
        <h2>Contact Details:</h2>
        <p><strong>Name:</strong> ${firstName} ${lastName}</p>
        <p><strong>Email:</strong> ${email}</p>
        <p><strong>Country:</strong> ${country}</p>
        <p><strong>Hometown:</strong> ${hometown}</p>
        
        <h2>Message:</h2>
        <p>${message}</p>
      </div>
    `;
    
    // Send email using Resend
    const { data, error } = await resend.emails.send({
      from: 'WebWeaver Contact Form <<EMAIL>>', // Update with your verified domain
      to: process.env.NOTIFICATION_EMAIL || '<EMAIL>', // Update with your email
      subject: `New Contact Form Submission from ${firstName} ${lastName}`,
      html: emailHtml, // Use HTML string instead of React component
    });
    
    if (error) {
      console.error('Error sending email:', error);
      return NextResponse.json(
        { error: 'Failed to send email' },
        { status: 500 }
      );
    }
    
    return NextResponse.json(
      { success: true, message: 'Email sent successfully', data },
      { status: 200 }
    );
    
  } catch (error) {
    console.error('Error processing request:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}