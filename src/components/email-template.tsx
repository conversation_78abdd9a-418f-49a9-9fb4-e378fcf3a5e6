import * as React from 'react';

interface EmailTemplateProps {
  firstName: string;
  lastName: string;
  email: string;
  country: string;
  hometown: string;
  message: string;
}

export const EmailTemplate: React.FC<Readonly<EmailTemplateProps>> = ({
  firstName,
  lastName,
  email,
  country,
  hometown,
  message,
}) => (
  <div>
    <h1>New Contact Form Submission</h1>
    <p>You have received a new message from your landing page contact form.</p>
    
    <h2>Contact Details:</h2>
    <p><strong>Name:</strong> {firstName} {lastName}</p>
    <p><strong>Email:</strong> {email}</p>
    <p><strong>Country:</strong> {country}</p>
    <p><strong>Hometown:</strong> {hometown}</p>
    
    <h2>Message:</h2>
    <p>{message}</p>
  </div>
);